{"version": 2, "dgSpecHash": "3KPaz4xcjcc=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\aforge\\2.2.5\\aforge.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aforge.video\\2.2.5\\aforge.video.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\aforge.video.directshow\\2.2.5\\aforge.video.directshow.2.2.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.2\\system.management.7.0.2.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "message": "Package 'AForge 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "libraryId": "<PERSON><PERSON><PERSON><PERSON>", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'AForge.Video 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "libraryId": "AForge.Video", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "message": "Package 'AForge.Video.DirectShow 2.2.5' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net6.0-windows7.0'. This package may not be fully compatible with your project.", "projectPath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\source\\repos\\diagnosticsTool\\diagnosticsTool.csproj", "libraryId": "AForge.Video.DirectShow", "targetGraphs": ["net6.0-windows7.0"]}]}