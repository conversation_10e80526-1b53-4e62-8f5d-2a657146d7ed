﻿using System.Diagnostics;
using System;
using System.Linq;
using System.Management;
using AForge.Video;
using AForge.Video.DirectShow;
using System.Windows.Forms;
using System.Diagnostics.Tracing;

namespace diagnosticsTool
{
    public partial class Form1 : Form
    {
        private FilterInfoCollection videoDevices;
        private VideoCaptureDevice videoSource;
        private bool isCapturing = false;

        public Form1()
        {
            InitializeComponent();
        }

        private void Button1_Click(object sender, EventArgs e)
        {
            Process.Start("mmc.exe", "devmgmt.msc");
        }

        private void Button2_Click(object sender, EventArgs e)
        {
            // Process.Start("mmc.exe", "devmgmt.msc");
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }

        private void pictureBox3_Click(object sender, EventArgs e)
        {
            pictureBox3.Image = Properties.Resources.Screenshot_2023_10_23_144947;
        }

        private void webCamBox_Click(object sender, EventArgs e)
        {
            if (isCapturing)
            {
                videoSource.SignalToStop();
                videoSource.WaitForStop();

                // Release the video source
                videoSource = null;

                isCapturing = false;
                webCamBox.Image = null; // Clear the PictureBox

            }
            else
            {
                videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

                if (videoDevices.Count == 0)
                {
                    MessageBox.Show("No video devices found.");
                    return;
                }

                videoSource = new VideoCaptureDevice(videoDevices[0].MonikerString);
                videoSource.NewFrame += new NewFrameEventHandler(video_NewFrame);
                videoSource.Start();

                isCapturing = true;
            }
        }

        private void video_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            // This event is triggered each time a new frame is captured.
            // You can process the frame here.
            webCamBox.Image = (Bitmap)eventArgs.Frame.Clone();
        }
    }
}