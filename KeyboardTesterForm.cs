using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace diagnosticsTool
{
    public partial class KeyboardTesterForm : Form
    {
        // Win32 API import for detecting specific left/right modifier keys
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        private Dictionary<Keys, Button> keyButtons = new Dictionary<Keys, Button>();
        private HashSet<Keys> testedKeys = new HashSet<Keys>();
        private Label statusLabel;
        private Button resetButton;
        private Panel mainKeyboardPanel;
        private Panel functionKeysPanel;
        private Panel numpadPanel;

        public KeyboardTesterForm()
        {
            InitializeComponent();
            InitializeKeyboard();
            this.KeyPreview = true;
            this.KeyDown += KeyboardTesterForm_KeyDown;
            this.KeyUp += KeyboardTesterForm_KeyUp;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 500);
            this.Text = "Professional Keyboard Tester v1.0";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(248, 248, 255);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Icon = SystemIcons.Application;
            
            // Status label
            statusLabel = new Label
            {
                Text = "Press any key to test it. Green = tested, Gray = untested, Yellow = currently pressed",
                Location = new Point(20, 20),
                Size = new Size(800, 20),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(25, 25, 112)
            };
            this.Controls.Add(statusLabel);

            // Reset button
            resetButton = new Button
            {
                Text = "🔄 Reset All Keys",
                Location = new Point(850, 15),
                Size = new Size(140, 35),
                BackColor = Color.FromArgb(220, 20, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            resetButton.FlatAppearance.BorderSize = 0;
            resetButton.Click += ResetButton_Click;
            this.Controls.Add(resetButton);
            
            // Function keys panel
            functionKeysPanel = new Panel
            {
                Location = new Point(20, 60),
                Size = new Size(800, 50),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 248, 255)
            };
            this.Controls.Add(functionKeysPanel);

            // Main keyboard panel
            mainKeyboardPanel = new Panel
            {
                Location = new Point(20, 120),
                Size = new Size(800, 320),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            this.Controls.Add(mainKeyboardPanel);

            // Numeric keypad panel
            numpadPanel = new Panel
            {
                Location = new Point(840, 120),
                Size = new Size(200, 320),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(245, 245, 245)
            };
            this.Controls.Add(numpadPanel);
            
            this.ResumeLayout(false);
        }

        private void InitializeKeyboard()
        {
            CreateFunctionKeys();
            CreateMainKeyboard();
            CreateNumericKeypad();
        }

        private void CreateFunctionKeys()
        {
            // Function keys F1-F12
            var functionKeys = new List<Keys>
            {
                Keys.F1, Keys.F2, Keys.F3, Keys.F4, Keys.F5, Keys.F6,
                Keys.F7, Keys.F8, Keys.F9, Keys.F10, Keys.F11, Keys.F12
            };

            int x = 10;
            foreach (var key in functionKeys)
            {
                Button btn = CreateKeyButton(key.ToString(), new Size(60, 30), new Point(x, 5));
                keyButtons[key] = btn;
                functionKeysPanel.Controls.Add(btn);
                x += 65;
            }
        }

        private void CreateMainKeyboard()
        {
            // Row 1: Numbers and symbols
            var row1Keys = new List<(string text, Keys key)>
            {
                ("`", Keys.Oemtilde), ("1", Keys.D1), ("2", Keys.D2), ("3", Keys.D3), ("4", Keys.D4),
                ("5", Keys.D5), ("6", Keys.D6), ("7", Keys.D7), ("8", Keys.D8), ("9", Keys.D9),
                ("0", Keys.D0), ("-", Keys.OemMinus), ("=", Keys.Oemplus), ("Backspace", Keys.Back)
            };

            // Row 2: QWERTY
            var row2Keys = new List<(string text, Keys key)>
            {
                ("Tab", Keys.Tab), ("Q", Keys.Q), ("W", Keys.W), ("E", Keys.E), ("R", Keys.R),
                ("T", Keys.T), ("Y", Keys.Y), ("U", Keys.U), ("I", Keys.I), ("O", Keys.O),
                ("P", Keys.P), ("[", Keys.OemOpenBrackets), ("]", Keys.OemCloseBrackets), ("\\", Keys.OemBackslash)
            };

            // Row 3: ASDF
            var row3Keys = new List<(string text, Keys key)>
            {
                ("Caps", Keys.CapsLock), ("A", Keys.A), ("S", Keys.S), ("D", Keys.D), ("F", Keys.F),
                ("G", Keys.G), ("H", Keys.H), ("J", Keys.J), ("K", Keys.K), ("L", Keys.L),
                (";", Keys.OemSemicolon), ("'", Keys.OemQuotes), ("Enter", Keys.Enter)
            };

            // Row 4: ZXCV
            var row4Keys = new List<(string text, Keys key)>
            {
                ("Shift", Keys.LShiftKey), ("Z", Keys.Z), ("X", Keys.X), ("C", Keys.C), ("V", Keys.V),
                ("B", Keys.B), ("N", Keys.N), ("M", Keys.M), (",", Keys.Oemcomma), (".", Keys.OemPeriod),
                ("/", Keys.OemQuestion), ("Shift", Keys.RShiftKey)
            };

            // Row 5: Bottom row
            var row5Keys = new List<(string text, Keys key)>
            {
                ("Ctrl", Keys.LControlKey), ("Win", Keys.LWin), ("Alt", Keys.LMenu), ("Space", Keys.Space),
                ("Alt", Keys.RMenu), ("Win", Keys.RWin), ("Menu", Keys.Apps), ("Ctrl", Keys.RControlKey)
            };

            var allRows = new List<List<(string text, Keys key)>> { row1Keys, row2Keys, row3Keys, row4Keys, row5Keys };

            int y = 10;
            foreach (var row in allRows)
            {
                int x = 10;
                foreach (var (text, key) in row)
                {
                    Size buttonSize = GetKeySize(text);
                    Button btn = CreateKeyButton(text, buttonSize, new Point(x, y));
                    keyButtons[key] = btn;
                    mainKeyboardPanel.Controls.Add(btn);
                    x += buttonSize.Width + 2;
                }
                y += 45;
            }

            // Navigation keys cluster (moved further right to avoid overlap with "\" key)
            var navKeys = new List<(string text, Keys key, Point location, Size size)>
            {
                // Top row of navigation keys
                ("Ins", Keys.Insert, new Point(650, 50), new Size(35, 25)),
                ("Home", Keys.Home, new Point(690, 50), new Size(35, 25)),
                ("PgUp", Keys.PageUp, new Point(730, 50), new Size(35, 25)),

                // Bottom row of navigation keys
                ("Del", Keys.Delete, new Point(650, 80), new Size(35, 25)),
                ("End", Keys.End, new Point(690, 80), new Size(35, 25)),
                ("PgDn", Keys.PageDown, new Point(730, 80), new Size(35, 25))
            };

            foreach (var (text, key, location, size) in navKeys)
            {
                Button btn = CreateKeyButton(text, size, location);
                keyButtons[key] = btn;
                mainKeyboardPanel.Controls.Add(btn);
            }

            // Arrow keys (properly positioned below navigation keys)
            var arrowKeys = new List<(string text, Keys key, Point location)>
            {
                ("↑", Keys.Up, new Point(690, 120)),
                ("←", Keys.Left, new Point(665, 145)),
                ("↓", Keys.Down, new Point(690, 145)),
                ("→", Keys.Right, new Point(715, 145))
            };

            foreach (var (text, key, location) in arrowKeys)
            {
                Button btn = CreateKeyButton(text, new Size(30, 25), location);
                keyButtons[key] = btn;
                mainKeyboardPanel.Controls.Add(btn);
            }
        }

        private void CreateNumericKeypad()
        {
            // Numeric keypad layout
            var numpadKeys = new List<(string text, Keys key, Point location, Size size)>
            {
                ("Num", Keys.NumLock, new Point(10, 10), new Size(40, 30)),
                ("/", Keys.Divide, new Point(55, 10), new Size(40, 30)),
                ("*", Keys.Multiply, new Point(100, 10), new Size(40, 30)),
                ("-", Keys.Subtract, new Point(145, 10), new Size(40, 30)),
                
                ("7", Keys.NumPad7, new Point(10, 45), new Size(40, 30)),
                ("8", Keys.NumPad8, new Point(55, 45), new Size(40, 30)),
                ("9", Keys.NumPad9, new Point(100, 45), new Size(40, 30)),
                ("+", Keys.Add, new Point(145, 45), new Size(40, 65)),
                
                ("4", Keys.NumPad4, new Point(10, 80), new Size(40, 30)),
                ("5", Keys.NumPad5, new Point(55, 80), new Size(40, 30)),
                ("6", Keys.NumPad6, new Point(100, 80), new Size(40, 30)),
                
                ("1", Keys.NumPad1, new Point(10, 115), new Size(40, 30)),
                ("2", Keys.NumPad2, new Point(55, 115), new Size(40, 30)),
                ("3", Keys.NumPad3, new Point(100, 115), new Size(40, 30)),
                ("Enter", Keys.Return, new Point(145, 115), new Size(40, 65)),
                
                ("0", Keys.NumPad0, new Point(10, 150), new Size(85, 30)),
                (".", Keys.Decimal, new Point(100, 150), new Size(40, 30))
            };

            foreach (var (text, key, location, size) in numpadKeys)
            {
                Button btn = CreateKeyButton(text, size, location);
                keyButtons[key] = btn;
                numpadPanel.Controls.Add(btn);
            }
        }

        private Button CreateKeyButton(string text, Size size, Point location)
        {
            return new Button
            {
                Text = text,
                Size = size,
                Location = location,
                BackColor = Color.FromArgb(240, 240, 240),
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 8, FontStyle.Bold),
                UseVisualStyleBackColor = false,
                ForeColor = Color.FromArgb(50, 50, 50),
                FlatAppearance = { BorderColor = Color.FromArgb(200, 200, 200), BorderSize = 1 }
            };
        }

        private Size GetKeySize(string keyText)
        {
            return keyText switch
            {
                "Backspace" => new Size(80, 40),
                "Tab" => new Size(60, 40),
                "Caps" => new Size(70, 40),
                "Enter" => new Size(80, 40),
                "Shift" => new Size(90, 40),
                "Ctrl" => new Size(50, 40),
                "Alt" => new Size(50, 40),
                "Win" => new Size(50, 40),
                "Menu" => new Size(50, 40),
                "Space" => new Size(300, 40),
                _ => new Size(40, 40)
            };
        }

        private void KeyboardTesterForm_KeyDown(object sender, KeyEventArgs e)
        {
            // Suppress system key handling for function keys and other special keys
            e.SuppressKeyPress = true;
            e.Handled = true;

            Keys keyToTest = e.KeyCode;

            // Handle shift key mapping - use Win32 API to detect which shift key
            if (e.KeyCode == Keys.ShiftKey)
            {
                // Check if right shift is pressed using Win32 API
                if ((GetAsyncKeyState(0xA1) & 0x8000) != 0) // VK_RSHIFT = 0xA1
                {
                    keyToTest = Keys.RShiftKey;
                }
                else if ((GetAsyncKeyState(0xA0) & 0x8000) != 0) // VK_LSHIFT = 0xA0
                {
                    keyToTest = Keys.LShiftKey;
                }
            }

            // Handle control key mapping
            if (e.KeyCode == Keys.ControlKey)
            {
                if ((GetAsyncKeyState(0xA3) & 0x8000) != 0) // VK_RCONTROL = 0xA3
                {
                    keyToTest = Keys.RControlKey;
                }
                else
                {
                    keyToTest = Keys.LControlKey;
                }
            }

            // Handle alt key mapping
            if (e.KeyCode == Keys.Menu)
            {
                if ((GetAsyncKeyState(0xA5) & 0x8000) != 0) // VK_RMENU = 0xA5
                {
                    keyToTest = Keys.RMenu;
                }
                else
                {
                    keyToTest = Keys.LMenu;
                }
            }

            // Handle Enter key mapping - Windows sends Keys.Return for both, need to distinguish
            if (e.KeyCode == Keys.Return)
            {
                // Use the extended key flag to distinguish between main Enter and numpad Enter
                // Main keyboard Enter has extended flag, numpad Enter doesn't
                if ((GetAsyncKeyState(0x0D) & 0x8000) != 0) // VK_RETURN = 0x0D
                {
                    // Check if this is an extended key (main keyboard) or not (numpad)
                    // We'll check the scan code or use a different approach
                    // For now, let's try both keys and see which one exists and hasn't been pressed recently
                    if (keyButtons.ContainsKey(Keys.Enter))
                    {
                        keyToTest = Keys.Enter; // Try main keyboard enter first
                    }
                    else if (keyButtons.ContainsKey(Keys.Return))
                    {
                        keyToTest = Keys.Return; // Fall back to numpad enter
                    }
                }
            }

            // Try to find the key in our button collection, with fallback options
            if (!keyButtons.ContainsKey(keyToTest))
            {
                // Handle some common key mapping issues
                // Note: Keys.Return and Keys.Enter have the same value, so we can't use switch
                if (e.KeyCode == Keys.Return)
                {
                    // If Return key not found, try Enter
                    if (keyButtons.ContainsKey(Keys.Enter))
                        keyToTest = Keys.Enter;
                    else if (keyButtons.ContainsKey(Keys.Return))
                        keyToTest = Keys.Return;
                }
            }

            if (keyButtons.ContainsKey(keyToTest))
            {
                testedKeys.Add(keyToTest);
                keyButtons[keyToTest].BackColor = Color.Yellow; // Currently pressed
                UpdateStatusLabel();
            }
        }

        private void KeyboardTesterForm_KeyUp(object sender, KeyEventArgs e)
        {
            e.Handled = true;

            Keys keyToTest = e.KeyCode;

            // Handle shift key mapping
            if (e.KeyCode == Keys.ShiftKey)
            {
                // Check which shift key was released
                if ((GetAsyncKeyState(0xA1) & 0x8000) == 0 && testedKeys.Contains(Keys.RShiftKey))
                {
                    keyToTest = Keys.RShiftKey;
                }
                else if ((GetAsyncKeyState(0xA0) & 0x8000) == 0 && testedKeys.Contains(Keys.LShiftKey))
                {
                    keyToTest = Keys.LShiftKey;
                }
            }

            // Handle control key mapping
            if (e.KeyCode == Keys.ControlKey)
            {
                if ((GetAsyncKeyState(0xA3) & 0x8000) == 0 && testedKeys.Contains(Keys.RControlKey))
                {
                    keyToTest = Keys.RControlKey;
                }
                else
                {
                    keyToTest = Keys.LControlKey;
                }
            }

            // Handle alt key mapping
            if (e.KeyCode == Keys.Menu)
            {
                if ((GetAsyncKeyState(0xA5) & 0x8000) == 0 && testedKeys.Contains(Keys.RMenu))
                {
                    keyToTest = Keys.RMenu;
                }
                else
                {
                    keyToTest = Keys.LMenu;
                }
            }

            // Handle Enter key mapping for KeyUp
            if (e.KeyCode == Keys.Return)
            {
                if (keyButtons.ContainsKey(Keys.Enter) && testedKeys.Contains(Keys.Enter))
                {
                    keyToTest = Keys.Enter;
                }
                else if (keyButtons.ContainsKey(Keys.Return) && testedKeys.Contains(Keys.Return))
                {
                    keyToTest = Keys.Return;
                }
            }

            // Try to find the key in our button collection, with fallback options
            if (!keyButtons.ContainsKey(keyToTest))
            {
                // Handle key mapping issues (Keys.Return and Keys.Enter have same value)
                if (e.KeyCode == Keys.Return)
                {
                    if (keyButtons.ContainsKey(Keys.Enter) && testedKeys.Contains(Keys.Enter))
                        keyToTest = Keys.Enter;
                    else if (keyButtons.ContainsKey(Keys.Return) && testedKeys.Contains(Keys.Return))
                        keyToTest = Keys.Return;
                }
            }

            if (keyButtons.ContainsKey(keyToTest))
            {
                keyButtons[keyToTest].BackColor = Color.LightGreen; // Tested
            }
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            testedKeys.Clear();
            foreach (var kvp in keyButtons)
            {
                kvp.Value.BackColor = Color.FromArgb(240, 240, 240); // Reset to original gray
            }
            UpdateStatusLabel();
        }

        private void UpdateStatusLabel()
        {
            int totalKeys = keyButtons.Count;
            int testedCount = testedKeys.Count;
            double percentage = totalKeys > 0 ? (double)testedCount / totalKeys * 100 : 0;
            statusLabel.Text = $"Keys tested: {testedCount}/{totalKeys} ({percentage:F1}%) - Press any key to test it. Green = tested, Gray = untested, Yellow = currently pressed";
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // Intercept all key presses to prevent system actions (like F1 opening help)
            // This ensures keys are only used for testing, not triggering system functions

            // Extract the base key without modifiers for testing
            Keys baseKey = keyData & Keys.KeyCode;

            // Handle special key mappings that Windows doesn't handle correctly
            Keys keyToTest = baseKey;

            // Special handling for Enter keys - check if it's numpad enter
            if (baseKey == Keys.Return)
            {
                // Check if numpad enter is pressed (it has a different scan code)
                // We can distinguish by checking if NumLock affects it
                bool isNumpadEnter = (keyData & Keys.Control) == 0 &&
                                   (GetAsyncKeyState(0x6D) & 0x8000) == 0; // Not numpad minus

                if (isNumpadEnter && keyButtons.ContainsKey(Keys.Return))
                {
                    keyToTest = Keys.Return; // Numpad Enter
                }
                else if (keyButtons.ContainsKey(Keys.Enter))
                {
                    keyToTest = Keys.Enter; // Main keyboard Enter
                }
            }

            // Handle backslash key specifically
            if (baseKey == Keys.OemBackslash || baseKey == Keys.Oem5)
            {
                if (keyButtons.ContainsKey(Keys.OemBackslash))
                {
                    keyToTest = Keys.OemBackslash;
                }
            }

            // Test the key
            if (keyButtons.ContainsKey(keyToTest))
            {
                testedKeys.Add(keyToTest);
                keyButtons[keyToTest].BackColor = Color.Yellow; // Currently pressed
                UpdateStatusLabel();

                // Use a timer to change color back to green after a brief moment
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 200;
                timer.Tick += (s, args) =>
                {
                    if (keyButtons.ContainsKey(keyToTest))
                    {
                        keyButtons[keyToTest].BackColor = Color.LightGreen;
                    }
                    timer.Stop();
                    timer.Dispose();
                };
                timer.Start();
            }

            // Return true to indicate we handled the key and prevent system processing
            return true;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Show completion message if all keys were tested
            if (testedKeys.Count == keyButtons.Count && keyButtons.Count > 0)
            {
                MessageBox.Show($"Congratulations! You have successfully tested all {keyButtons.Count} keys on your keyboard!",
                    "Keyboard Test Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            base.OnFormClosing(e);
        }
    }
}
